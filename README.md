# emergentRPG

**An AI-Driven Storytelling RPG Framework**

emergentRPG is a revolutionary role-playing game framework that eliminates traditional hardcoded game mechanics in favor of AI-driven dynamic systems. Every aspect of the game—from character development to world changes—is generated by AI to serve compelling, personalized storytelling experiences.

## 🎯 Core Philosophy

Unlike traditional RPGs with predetermined mechanics, emergentRPG operates as an **AI-driven storytelling framework** where:

- **All world changes are AI-generated** and contextually appropriate
- **Character development emerges from AI analysis** of player behavior and choices
- **Player actions have AI-generated consequences** with narrative significance
- **Zero hardcoded game mechanics** - all systems are AI-driven
- **Components guide AI models** in creating compelling, consistent stories

## ✨ Key Features

### AI-Driven Dynamic Systems
- **Dynamic World Management**: Environments react intelligently to player actions
- **Character Development**: Progression based on actual choices and behavior patterns
- **Consequence System**: Meaningful, delayed consequences that enhance storytelling
- **Quest Generation**: Contextual quests that emerge from the current story state
- **Item Generation**: Equipment and items with narrative significance

### Advanced Backend Architecture
- **FastAPI-based REST API** with comprehensive endpoints
- **MongoDB database** with optimized async operations
- **Gemini AI integration** for all dynamic content generation
- **Caching system** with Redis for performance optimization
- **Feature flags** for controlled feature rollouts
- **WebSocket support** for real-time gameplay

### Storytelling Framework
- **Scenario Generation**: AI creates complete scenarios from series analysis
- **Lorebook System**: Rich world-building with characters, locations, and lore
- **Narrative Templates**: AI-generated initial narratives for scenarios
- **Real-time Gameplay**: Dynamic story progression with AI responses

## 🚀 Quick Start

### Prerequisites

- **Python 3.9+** (recommended: use pyenv for version management)
- **MongoDB** (local installation or MongoDB Atlas)
- **Redis** (for caching)
- **Google AI API Key** (for Gemini integration)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/v1un/emergentRPG.git
   cd emergentRPG
   ```

2. **Set up Python environment with pyenv** (recommended)
   ```bash
   # Install pyenv if not already installed
   curl https://pyenv.run | bash
   
   # Install and set Python version
   pyenv install 3.11.0
   pyenv local 3.11.0
   
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. **Environment Configuration**
   
   Create a `.env` file in the `backend` directory:
   ```env
   # Database Configuration
   MONGO_URL=mongodb://localhost:27017
   DATABASE_NAME=emergent_rpg
   
   # AI Configuration
   GOOGLE_API_KEY=your_google_ai_api_key_here
   GEMINI_MODEL=gemini-2.5-flash-preview-05-20
   GEMINI_REQUESTS_PER_MINUTE=60
   MAX_CONTEXT_LENGTH=32000
   AI_TEMPERATURE=0.7
   AI_MAX_TOKENS=2048
   
   # API Configuration
   CORS_ORIGINS=http://localhost:3000
   DEBUG=True
   LOG_LEVEL=INFO
   HOST=127.0.0.1
   PORT=8001
   
   # Cache Configuration
   REDIS_URL=redis://localhost:6379
   CACHE_TTL=3600
   ```

5. **Start the services**
   ```bash
   # Start MongoDB (if running locally)
   mongod
   
   # Start Redis (if running locally)
   redis-server
   
   # Start the backend server
   cd backend
   uvicorn main:app --reload --host 127.0.0.1 --port 8001
   ```

6. **Verify installation**
   
   Visit `http://localhost:8001/api/health` to check if all services are running correctly.

## 🏗️ Project Structure

```
emergentRPG/
├── backend/                    # Backend API server
│   ├── config/                # Configuration management
│   │   ├── settings.py        # Application settings
│   │   ├── feature_flags.py   # Feature flag management
│   │   └── configuration_manager.py
│   ├── flows/                 # AI workflow definitions
│   │   ├── character_generation/
│   │   ├── gameplay/          # Real-time gameplay flows
│   │   ├── lorebook_generation/
│   │   └── series_analysis/
│   ├── models/                # Data models
│   │   ├── game_models.py     # Game session, character, world state
│   │   └── scenario_models.py # Scenarios, lorebooks, generation
│   ├── services/              # Business logic services
│   │   ├── ai/               # AI-driven systems
│   │   ├── cache/            # Caching management
│   │   ├── config/           # Configuration services
│   │   ├── database_service.py
│   │   └── scenario_generation/
│   ├── utils/                # Utilities
│   │   └── gemini_client.py  # AI client integration
│   ├── tests/                # Test suite
│   ├── main.py               # FastAPI application
│   └── requirements.txt      # Python dependencies
├── docs/                     # Documentation
├── logs/                     # Application logs
└── README.md                 # This file
```

## 🎮 Basic Usage

### 1. Generate a Scenario

Create an AI-generated scenario from a series:

```bash
curl -X POST "http://localhost:8001/api/scenarios/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "series_title": "The Lord of the Rings",
    "series_type": "book",
    "genre": "fantasy",
    "setting": "Middle-earth",
    "power_system": "Magic and divine intervention"
  }'
```

### 2. Create a Game Session

Start a new game session:

```bash
curl -X POST "http://localhost:8001/api/game/sessions" \
  -H "Content-Type: application/json" \
  -d '{
    "lorebook_id": "your_lorebook_id",
    "character_name": "Aragorn",
    "scenario_template_id": "your_scenario_id"
  }'
```

### 3. Perform Game Actions

Take actions in the game:

```bash
curl -X POST "http://localhost:8001/api/game/sessions/{session_id}/action" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "I examine the ancient ruins for any signs of danger"
  }'
```

## 🔧 Development Environment Setup

### Using pyenv (Recommended)

emergentRPG is designed to work seamlessly with pyenv for Python version management:

```bash
# Install pyenv (if not already installed)
curl https://pyenv.run | bash

# Add to your shell profile (.bashrc, .zshrc, etc.)
echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc
echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc
echo 'eval "$(pyenv init -)"' >> ~/.bashrc

# Reload shell
source ~/.bashrc

# Install Python version
pyenv install 3.11.0
pyenv local 3.11.0

# Verify installation
python --version  # Should show Python 3.11.0
```

### Development Tools

The project includes development tools for code quality:

```bash
# Format code
cd backend
./format.sh

# Lint code
./lint.sh

# Run tests
python -m pytest tests/ -v
```

## 🌐 API Overview

The backend provides a comprehensive REST API with the following main endpoints:

### Core Endpoints
- `GET /api/health` - Health check with service status
- `GET /api/performance` - Performance metrics and monitoring

### Scenario Generation
- `POST /api/scenarios/generate` - Start AI scenario generation
- `GET /api/scenarios/status/{task_id}` - Check generation progress
- `GET /api/scenarios/templates` - List available scenario templates

### Game Sessions
- `POST /api/game/sessions` - Create new game session
- `GET /api/game/sessions/{session_id}` - Get session details
- `POST /api/game/sessions/{session_id}/action` - Perform game action
- `PUT /api/game/sessions/{session_id}` - Save session state

### Lorebooks & World Data
- `GET /api/lorebooks` - List available lorebooks
- `GET /api/lorebooks/{lorebook_id}` - Get lorebook details
- `DELETE /api/lorebooks/{lorebook_id}` - Delete lorebook

### AI Services
- `POST /api/ai/generate-response` - Generate AI narrative response
- `POST /api/ai/validate-action` - Validate player action

### Configuration & Features
- `GET /api/config/settings` - Get application configuration
- `GET /api/features` - List feature flags
- `POST /api/features/{feature_name}` - Update feature flag

For detailed API documentation with request/response examples, see [BACKEND_AND_GAME_LOGIC.md](BACKEND_AND_GAME_LOGIC.md).

## 🧪 Testing

Run the comprehensive test suite:

```bash
cd backend
python -m pytest tests/ -v --cov=. --cov-report=html
```

Test categories:
- **Unit tests**: Individual component testing
- **Integration tests**: Service interaction testing
- **AI system tests**: Dynamic system behavior testing
- **API tests**: Endpoint functionality testing

## 🤝 Contributing

We welcome contributions to emergentRPG! Please follow these guidelines:

### Development Workflow

1. **Fork the repository** and create a feature branch
2. **Set up development environment** using pyenv and virtual environment
3. **Make your changes** following the existing code style
4. **Run tests** to ensure everything works correctly
5. **Format and lint** your code using the provided scripts
6. **Submit a pull request** with a clear description of changes

### Code Style

- **Python**: Follow PEP 8, use Black for formatting
- **Type hints**: Use type annotations for all functions
- **Documentation**: Add docstrings for all public functions and classes
- **Testing**: Write tests for new functionality

### AI System Guidelines

When working with AI-driven systems:
- **Maintain fallback mechanisms** for reliability
- **Preserve context consistency** across AI calls
- **Follow the storytelling-first philosophy**
- **Ensure all changes serve narrative purposes**

## 📚 Documentation

- **[BACKEND_AND_GAME_LOGIC.md](BACKEND_AND_GAME_LOGIC.md)** - Comprehensive technical documentation
- **[backend/docs/ai_integration_complete.md](backend/docs/ai_integration_complete.md)** - AI systems integration details
- **[backend/BACKEND_IMPROVEMENTS_SUMMARY.md](backend/BACKEND_IMPROVEMENTS_SUMMARY.md)** - Recent improvements summary

## 🔗 Links

- **Repository**: [https://github.com/v1un/emergentRPG](https://github.com/v1un/emergentRPG)
- **Issues**: [https://github.com/v1un/emergentRPG/issues](https://github.com/v1un/emergentRPG/issues)
- **Discussions**: [https://github.com/v1un/emergentRPG/discussions](https://github.com/v1un/emergentRPG/discussions)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google Gemini AI** for powering the dynamic content generation
- **FastAPI** for the excellent web framework
- **MongoDB** for flexible data storage
- **The open-source community** for the amazing tools and libraries

---

**emergentRPG**: Where AI and storytelling converge to create truly emergent role-playing experiences.
