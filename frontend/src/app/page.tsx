// Main Game Page - Session selection and game interface

'use client';

import React from 'react';
import { GameLayout } from '@/components/layout/GameLayout';
import { SessionManager } from '@/components/session/SessionManager';
import { useGameStore } from '@/stores/gameStore';
import { GameSession } from '@/types';

export default function Home() {
  const { currentSession, setCurrentSession } = useGameStore();

  const handleSessionSelect = (session: GameSession) => {
    setCurrentSession(session);
  };

  return (
    <GameLayout>
      {!currentSession ? (
        <SessionManager onSessionSelect={handleSessionSelect} />
      ) : (
        // Game interface is rendered by MainContent in GameLayout
        <div className="h-full">
          {/* Game panels are handled by MainContent based on activePanel */}
        </div>
      )}
    </GameLayout>
  );
}
