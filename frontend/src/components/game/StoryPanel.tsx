// Story Panel Component - Main storytelling interface

'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useCurrentSession, useCurrentStory, useIsAIGenerating, useGameStore } from '@/stores/gameStore';
import { useGameAction } from '@/hooks/useGameAction';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card } from '@/components/ui/Card';
import { TypingIndicator } from '@/components/ui/Loading';
import { cn, truncateText } from '@/utils/helpers';
import { gameFormatters } from '@/utils/formatting';
import { actionSchema } from '@/utils/validation';
import { StoryEntry, ActionType } from '@/types';
import { ACTION_TYPES } from '@/utils/constants';
import {
  PaperAirplaneIcon,
  BookmarkIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  DocumentTextIcon,
  FolderIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import {
  BookmarkIcon as BookmarkSolidIcon,
  StarIcon as StarSolidIcon,
} from '@heroicons/react/24/solid';

export function StoryPanel() {
  const currentSession = useCurrentSession();
  const story = useCurrentStory();
  const isAIGenerating = useIsAIGenerating();
  const { updateSession } = useGameStore();

  const [actionInput, setActionInput] = useState('');
  const [actionError, setActionError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [bookmarkedEntries, setBookmarkedEntries] = useState<Set<string>>(new Set());
  const [showBookmarks, setShowBookmarks] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const storyEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const { performAction, isLoading, error } = useGameAction(
    currentSession?.session_id || '',
    {
      enableOptimisticUpdates: true,
      enableWebSocket: true,
      onSuccess: () => {
        setActionInput('');
        setActionError('');
      },
      onError: (err) => {
        setActionError(err.message);
      },
    }
  );

  // Auto-scroll to bottom when new story entries are added
  useEffect(() => {
    if (storyEndRef.current) {
      storyEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [story]);

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSubmitAction = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!actionInput.trim()) {
      setActionError('Please enter an action');
      return;
    }

    // Validate action
    const validation = actionSchema.safeParse(actionInput.trim());
    if (!validation.success) {
      setActionError(validation.error.errors[0].message);
      return;
    }

    try {
      await performAction(actionInput.trim());
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitAction(e);
    }
  };

  const getEntryIcon = (type: ActionType) => {
    switch (type) {
      case ACTION_TYPES.PLAYER:
        return '👤';
      case ACTION_TYPES.NARRATION:
        return '📖';
      case ACTION_TYPES.ACTION:
        return '⚡';
      case ACTION_TYPES.SYSTEM:
        return '🔧';
      default:
        return '💬';
    }
  };

  const getEntryStyle = (type: ActionType) => {
    switch (type) {
      case ACTION_TYPES.PLAYER:
        return 'bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800';
      case ACTION_TYPES.NARRATION:
        return 'bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700';
      case ACTION_TYPES.ACTION:
        return 'bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800';
      case ACTION_TYPES.SYSTEM:
        return 'bg-yellow-50 dark:bg-yellow-950 border-yellow-200 dark:border-yellow-800';
      default:
        return 'bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700';
    }
  };

  // Story enhancement functions
  const handleBookmarkEntry = (entryId: string) => {
    setBookmarkedEntries(prev => {
      const newSet = new Set(prev);
      if (newSet.has(entryId)) {
        newSet.delete(entryId);
      } else {
        newSet.add(entryId);
      }
      return newSet;
    });
  };

  const handleExportStory = (format: 'markdown' | 'pdf' | 'txt') => {
    const storyText = story.map(entry => {
      const timestamp = new Date(entry.timestamp).toLocaleString();
      const type = gameFormatters.actionType(entry.type as ActionType);
      return `[${timestamp}] ${type}: ${entry.text}`;
    }).join('\n\n');

    const filename = `${currentSession?.session_id || 'story'}_${new Date().toISOString().split('T')[0]}`;

    if (format === 'markdown') {
      const markdownContent = story.map(entry => {
        const timestamp = new Date(entry.timestamp).toLocaleString();
        const type = gameFormatters.actionType(entry.type as ActionType);
        return `## ${type}\n*${timestamp}*\n\n${entry.text}\n`;
      }).join('\n---\n\n');

      downloadFile(markdownContent, `${filename}.md`, 'text/markdown');
    } else if (format === 'txt') {
      downloadFile(storyText, `${filename}.txt`, 'text/plain');
    } else if (format === 'pdf') {
      // TODO: Implement PDF export
      console.log('PDF export not yet implemented');
    }

    setShowExportMenu(false);
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const filteredStory = story.filter(entry => {
    if (!searchQuery) return true;
    return entry.text.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const displayedStory = showBookmarks
    ? filteredStory.filter(entry => bookmarkedEntries.has(entry.id))
    : filteredStory;

  if (!currentSession) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-muted-foreground">No active game session</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Story Header with Controls */}
      <div className="border-b border-gray-200 dark:border-gray-800 p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Story</h2>
          <div className="flex items-center space-x-2">
            {/* Search Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSearch(!showSearch)}
              className={cn(showSearch && 'bg-blue-50 dark:bg-blue-950')}
            >
              <MagnifyingGlassIcon className="h-4 w-4" />
            </Button>

            {/* Bookmarks Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowBookmarks(!showBookmarks)}
              className={cn(showBookmarks && 'bg-yellow-50 dark:bg-yellow-950')}
            >
              <BookmarkIcon className="h-4 w-4" />
              {bookmarkedEntries.size > 0 && (
                <span className="ml-1 text-xs">({bookmarkedEntries.size})</span>
              )}
            </Button>

            {/* Export Menu */}
            <div className="relative">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowExportMenu(!showExportMenu)}
              >
                <ArrowDownTrayIcon className="h-4 w-4" />
              </Button>

              {showExportMenu && (
                <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleExportStory('markdown')}
                      className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <DocumentTextIcon className="h-4 w-4 inline mr-2" />
                      Export as Markdown
                    </button>
                    <button
                      onClick={() => handleExportStory('txt')}
                      className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <DocumentTextIcon className="h-4 w-4 inline mr-2" />
                      Export as Text
                    </button>
                    <button
                      onClick={() => handleExportStory('pdf')}
                      className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-400"
                      disabled
                    >
                      <DocumentTextIcon className="h-4 w-4 inline mr-2" />
                      Export as PDF (Coming Soon)
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Search Bar */}
        {showSearch && (
          <div className="mt-3">
            <Input
              placeholder="Search story entries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<MagnifyingGlassIcon className="h-4 w-4" />}
            />
          </div>
        )}

        {/* Filter Status */}
        {(showBookmarks || searchQuery) && (
          <div className="mt-2 text-sm text-muted-foreground">
            {showBookmarks && `Showing ${displayedStory.length} bookmarked entries`}
            {showBookmarks && searchQuery && ' • '}
            {searchQuery && `Filtered by "${searchQuery}"`}
            {displayedStory.length !== story.length && (
              <button
                onClick={() => {
                  setShowBookmarks(false);
                  setSearchQuery('');
                }}
                className="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
              >
                Clear filters
              </button>
            )}
          </div>
        )}
      </div>

      {/* Story Display */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {displayedStory.length === 0 ? (
          <div className="text-center py-12">
            {story.length === 0 ? (
              <>
                <h3 className="text-lg font-medium text-foreground mb-2">
                  Your Adventure Begins
                </h3>
                <p className="text-muted-foreground mb-4">
                  Enter your first action to start your AI-driven story adventure.
                </p>
              </>
            ) : (
              <>
                <h3 className="text-lg font-medium text-foreground mb-2">
                  No Entries Found
                </h3>
                <p className="text-muted-foreground mb-4">
                  {showBookmarks
                    ? "No bookmarked entries found. Bookmark entries by clicking the star icon."
                    : "No entries match your search criteria."
                  }
                </p>
              </>
            )}
          </div>
        ) : (
          displayedStory.map((entry: StoryEntry, index: number) => (
            <Card
              key={entry.id}
              className={cn(
                'p-4 transition-all duration-200 group relative',
                getEntryStyle(entry.type as ActionType),
                bookmarkedEntries.has(entry.id) && 'ring-1 ring-yellow-300 dark:ring-yellow-700'
              )}
            >
              {/* Bookmark Button */}
              <button
                onClick={() => handleBookmarkEntry(entry.id)}
                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                {bookmarkedEntries.has(entry.id) ? (
                  <BookmarkSolidIcon className="h-4 w-4 text-yellow-500" />
                ) : (
                  <BookmarkIcon className="h-4 w-4 text-gray-400 hover:text-yellow-500" />
                )}
              </button>

              <div className="flex items-start space-x-3">
                <span className="text-lg flex-shrink-0 mt-1">
                  {getEntryIcon(entry.type as ActionType)}
                </span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-foreground">
                      {gameFormatters.actionType(entry.type as ActionType)}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {gameFormatters.storyTimestamp(entry.timestamp)}
                    </span>
                  </div>
                  <div className="prose prose-sm dark:prose-invert max-w-none">
                    <p className="text-foreground whitespace-pre-wrap">
                      {entry.text}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          ))
        )}

        {/* AI Generating Indicator */}
        {isAIGenerating && (
          <Card className="p-4 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-3">
              <span className="text-lg">🤖</span>
              <TypingIndicator message="AI is crafting your story..." />
            </div>
          </Card>
        )}

        <div ref={storyEndRef} />
      </div>

      {/* Action Input */}
      <div className="border-t border-gray-200 dark:border-gray-800 bg-card p-6">
        <form onSubmit={handleSubmitAction} className="space-y-4">
          <div className="flex space-x-2">
            <Input
              ref={inputRef}
              value={actionInput}
              onChange={(e) => setActionInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="What do you do? (Press Enter to submit)"
              error={actionError || error?.message}
              className="flex-1"
              disabled={isLoading || isAIGenerating}
              maxLength={500}
            />
            <Button
              type="submit"
              disabled={isLoading || isAIGenerating || !actionInput.trim()}
              loading={isLoading}
              className="px-4"
            >
              <PaperAirplaneIcon className="h-4 w-4" />
            </Button>
          </div>

          {/* Action Suggestions */}
          <div className="flex flex-wrap gap-2">
            {currentSession.world_state.available_actions?.slice(0, 3).map((suggestion, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setActionInput(suggestion)}
                disabled={isLoading || isAIGenerating}
                className="text-xs"
              >
                {truncateText(suggestion, 30)}
              </Button>
            ))}
          </div>

          {/* Character count */}
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <span>
              {actionInput.length}/500 characters
            </span>
            <span>
              Press Enter to submit, Shift+Enter for new line
            </span>
          </div>
        </form>
      </div>
    </div>
  );
}

export default StoryPanel;
