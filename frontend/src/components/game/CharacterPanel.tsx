// Character Panel Component - Character sheet and stats

'use client';

import React from 'react';
import { useCurrentCharacter } from '@/stores/gameStore';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { cn } from '@/utils/helpers';
import { gameFormatters, numberFormatters } from '@/utils/formatting';
import { CharacterStats } from '@/types';
import {
  UserIcon,
  HeartIcon,
  SparklesIcon,
  TrophyIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';

export function CharacterPanel() {
  const character = useCurrentCharacter();

  if (!character) {
    return (
      <div className="h-full flex items-center justify-center p-6">
        <div className="text-center">
          <UserIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            No Character Loaded
          </h3>
          <p className="text-muted-foreground">
            Start a game session to view character information.
          </p>
        </div>
      </div>
    );
  }

  const healthPercentage = (character.health / character.max_health) * 100;
  const manaPercentage = (character.mana / character.max_mana) * 100;
  const experienceToNext = character.level * 1000; // Simple calculation

  const getStatColor = (value: number) => {
    if (value >= 16) return 'text-purple-600 dark:text-purple-400';
    if (value >= 14) return 'text-blue-600 dark:text-blue-400';
    if (value >= 12) return 'text-green-600 dark:text-green-400';
    if (value >= 10) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getStatModifier = (value: number) => {
    const modifier = Math.floor((value - 10) / 2);
    return modifier >= 0 ? `+${modifier}` : `${modifier}`;
  };

  const renderProgressBar = (current: number, max: number, color: string) => (
    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
      <div
        className={cn('h-2 rounded-full transition-all duration-300', color)}
        style={{ width: `${Math.min(100, (current / max) * 100)}%` }}
      />
    </div>
  );

  const renderStatBlock = (stats: CharacterStats) => (
    <div className="grid grid-cols-2 gap-4">
      {Object.entries(stats).map(([statName, value]) => (
        <div key={statName} className="text-center p-3 bg-muted rounded-lg">
          <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-1">
            {statName.slice(0, 3)}
          </div>
          <div className={cn('text-2xl font-bold', getStatColor(value))}>
            {value}
          </div>
          <div className="text-xs text-muted-foreground">
            {getStatModifier(value)}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="h-full overflow-y-auto p-6 space-y-6">
      {/* Character Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
              <UserIcon className="h-8 w-8 text-primary-foreground" />
            </div>
            <div>
              <CardTitle className="text-2xl">{character.name}</CardTitle>
              <p className="text-muted-foreground">
                Level {character.level} {character.class_name}
              </p>
              {character.background && (
                <p className="text-sm text-muted-foreground">
                  {character.background}
                </p>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Health and Mana */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <HeartIcon className="h-5 w-5 mr-2 text-red-500" />
              Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{character.health}</span>
                <span>{character.max_health}</span>
              </div>
              {renderProgressBar(character.health, character.max_health, 'bg-red-500')}
              <p className="text-xs text-muted-foreground">
                {healthPercentage.toFixed(1)}% remaining
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <SparklesIcon className="h-5 w-5 mr-2 text-blue-500" />
              Mana
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{character.mana}</span>
                <span>{character.max_mana}</span>
              </div>
              {renderProgressBar(character.mana, character.max_mana, 'bg-blue-500')}
              <p className="text-xs text-muted-foreground">
                {manaPercentage.toFixed(1)}% remaining
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Experience */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <TrophyIcon className="h-5 w-5 mr-2 text-yellow-500" />
            Experience
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{numberFormatters.withSeparators(character.experience)} XP</span>
              <span>Level {character.level + 1}</span>
            </div>
            {renderProgressBar(character.experience % experienceToNext, experienceToNext, 'bg-yellow-500')}
            <p className="text-xs text-muted-foreground">
              {numberFormatters.withSeparators(experienceToNext - (character.experience % experienceToNext))} XP to next level
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Ability Scores */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <ShieldCheckIcon className="h-5 w-5 mr-2 text-green-500" />
            Ability Scores
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderStatBlock(character.stats)}
        </CardContent>
      </Card>

      {/* Equipment */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Equipment</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(character.equipped_items).map(([slot, itemName]) => (
              <div key={slot} className="flex justify-between items-center p-2 bg-muted rounded">
                <span className="text-sm font-medium capitalize">
                  {slot.replace('_', ' ')}
                </span>
                <span className="text-sm text-muted-foreground">
                  {itemName || 'None'}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Carry Weight */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Carry Capacity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current Load</span>
              <span>{gameFormatters.carryCapacity(0, character.max_carry_weight)}</span>
            </div>
            {renderProgressBar(0, character.max_carry_weight, 'bg-gray-500')}
            <p className="text-xs text-muted-foreground">
              You can carry {gameFormatters.weight(character.max_carry_weight)} total
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default CharacterPanel;
