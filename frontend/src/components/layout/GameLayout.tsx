// Main Game Layout Component

'use client';

import React, { useState } from 'react';
import { useGameStore } from '@/stores/gameStore';
import { cn } from '@/utils/helpers';
import { DEFAULT_VALUES } from '@/utils/constants';

// Import layout components (to be created)
import Header from './Header';
import Sidebar from './Sidebar';
import MainContent from './MainContent';
import Footer from './Footer';

interface GameLayoutProps {
  children: React.ReactNode;
}

export function GameLayout({ children }: GameLayoutProps) {
  const { sidebarCollapsed, setSidebarCollapsed } = useGameStore();
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive behavior
  React.useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      
      // Auto-collapse sidebar on mobile
      if (mobile && !sidebarCollapsed) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [sidebarCollapsed, setSidebarCollapsed]);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Header */}
      <Header 
        onToggleSidebar={toggleSidebar}
        sidebarCollapsed={sidebarCollapsed}
        isMobile={isMobile}
      />

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sidebar */}
        <Sidebar 
          collapsed={sidebarCollapsed}
          isMobile={isMobile}
          onClose={() => setSidebarCollapsed(true)}
        />

        {/* Main Content Area */}
        <main 
          className={cn(
            'flex-1 flex flex-col transition-all duration-300 ease-in-out',
            sidebarCollapsed ? 'ml-0' : `ml-${DEFAULT_VALUES.SIDEBAR_WIDTH}px`,
            isMobile && 'ml-0'
          )}
        >
          <MainContent>
            {children}
          </MainContent>
          
          {/* Footer */}
          <Footer />
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobile && !sidebarCollapsed && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarCollapsed(true)}
        />
      )}
    </div>
  );
}

export default GameLayout;
