# =============================================================================
# PYTHON
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python virtual environments
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/

# Python testing and coverage
.pytest_cache/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.tox/
.nox/

# Python type checking and linting
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/
.ruff_cache/

# =============================================================================
# NODE.JS / FRONTEND
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Package manager files
.npm
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Frontend caches and temporary files
.eslintcache
.stylelintcache
.cache/
.parcel-cache/
.temp/

# Frontend framework specific
.next/
.nuxt/
.docusaurus/
.vuepress/dist/
.serverless/
.fusebox/
.dynamodb/
.tern-port

# Frontend build outputs
build/
dist/
out/

# Frontend specific build files
frontend/build/
frontend/dist/
frontend/.next/
frontend/out/

# =============================================================================
# ENVIRONMENT VARIABLES
# =============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# =============================================================================
# IDE AND EDITOR FILES
# =============================================================================
# VSCode
.vscode/
.vscode-test/

# IntelliJ IDEA
.idea/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo

# Emacs
*~

# Eclipse
.project
.classpath
.settings/
*.launch

# Cloud9
.c9/

# Local History for Visual Studio Code
.history/

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Windows
ehthumbs.db
Thumbs.db
*.desktop

# Linux
.directory

# =============================================================================
# LOGS AND DEBUGGING
# =============================================================================
logs/
*.log
pids/
*.pid
*.seed
*.pid.lock

# =============================================================================
# TESTING
# =============================================================================
coverage/
.nyc_output/
*.lcov
test-results/
jest-results/
cypress/videos/
cypress/screenshots/

# =============================================================================
# TEMPORARY FILES
# =============================================================================
*.tmp
*.temp
*.bak
*.backup
*.old

# =============================================================================
# DATABASE FILES
# =============================================================================
*.db
*.sqlite
*.sqlite3

# =============================================================================
# STATIC FILES AND UPLOADS
# =============================================================================
staticfiles/
media/
uploads/

# =============================================================================
# SECRETS AND CREDENTIALS
# =============================================================================
*.pem
*.key
*.crt
*.p12
secrets/
credentials/

# =============================================================================
# PROJECT SPECIFIC
# =============================================================================
# emergentRPG specific
.qodo/
test_result.md
appmap.log
backups/

# Optional: Package lock files (uncomment to ignore)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# =============================================================================
# MISCELLANEOUS
# =============================================================================
# Output of 'npm pack'
*.tgz

# Optional REPL history
.node_repl_history