"""
Comprehensive tests for the game logic fixes
Tests all the major issues identified in the analysis
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import uuid

from models.game_models import (
    Character, InventoryItem, Quest, QuestProgress,
    EquipmentSlot, StoryEntry, ActionType
)
from models.scenario_models import LoreCharacter
from services.inventory.inventory_manager import InventoryManager
from utils.exceptions import (
    InvalidItemQuantityError, InsufficientCarryCapacityError,
    ValidationError
)


class TestCharacterModelFixes:
    """Test fixes for Character model conflicts"""

    def test_character_vs_lore_character_separation(self):
        """Test that <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> are separate models"""
        # Game Character
        game_char = Character(
            name="Hero",
            level=5,
            health=100,
            max_health=100,
            experience=250
        )

        # Lore Character
        lore_char = Lore<PERSON>haracter(
            name="Hero",
            role="protagonist",
            description="A brave adventurer"
        )

        # They should be different types
        assert type(game_char).__name__ == "Character"
        assert type(lore_char).__name__ == "LoreCharacter"

        # Game character should have game-specific fields
        assert hasattr(game_char, 'level')
        assert hasattr(game_char, 'experience')
        assert hasattr(game_char, 'equipped_items')

        # Lore character should have lore-specific fields
        assert hasattr(lore_char, 'role')
        assert hasattr(lore_char, 'personality')
        assert not hasattr(lore_char, 'level')

    def test_character_level_up_methods(self):
        """Test new Character level-up helper methods"""
        char = Character(name="Test", experience=150, level=1)

        # Should be able to level up
        assert char.can_level_up() == True
        assert char.experience_to_next_level == -50  # 100 - 150

        # After leveling up
        char.level = 2
        assert char.can_level_up() == False
        assert char.experience_to_next_level == 50  # 200 - 150


class TestEquipmentSystemFixes:
    """Test equipment slot system fixes"""

    def test_equipment_slots_enum(self):
        """Test EquipmentSlot enum values"""
        assert EquipmentSlot.WEAPON == "weapon"
        assert EquipmentSlot.HELMET == "helmet"
        assert EquipmentSlot.CHEST == "chest"

    def test_inventory_item_with_equipment_slot(self):
        """Test InventoryItem with equipment slot"""
        sword = InventoryItem(
            id=str(uuid.uuid4()),
            name="Iron Sword",
            type="weapon",
            equipment_slot=EquipmentSlot.WEAPON,
            weight=3.0
        )

        assert sword.equipment_slot == EquipmentSlot.WEAPON
        assert abs(sword.weight - 3.0) < 1e-6

    def test_character_equipped_items_tracking(self):
        """Test Character equipped items tracking"""
        char = Character(name="Test")
        sword_id = str(uuid.uuid4())

        # Equip item
        char.equipped_items[EquipmentSlot.WEAPON] = sword_id

        assert EquipmentSlot.WEAPON in char.equipped_items
        assert char.equipped_items[EquipmentSlot.WEAPON] == sword_id


class TestQuestSystemFixes:
    """Test quest system improvements"""

    def test_quest_progress_structure(self):
        """Test structured quest progress"""
        progress = QuestProgress(current=2, total=5)

        assert progress.current == 2
        assert progress.total == 5
        assert abs(progress.percentage - 40.0) < 1e-6
        assert progress.is_complete == False
        assert str(progress) == "2/5"

        # Complete quest
        progress.current = 5
        assert progress.is_complete == True
        assert abs(progress.percentage - 100.0) < 1e-6

    def test_quest_with_structured_progress(self):
        """Test Quest with QuestProgress"""
        objectives = ["Find the artifact", "Return to town"]
        quest = Quest(
            id=str(uuid.uuid4()),
            title="Test Quest",
            description="A test quest",
            progress=QuestProgress(
                current=0,
                total=len(objectives),
                completed_objectives=[False, False]
            ),
            objectives=objectives
        )

        assert quest.progress.total == 2
        assert quest.progress.is_complete == False

        # Complete first objective
        quest.progress.current = 1
        quest.progress.completed_objectives[0] = True

        assert abs(quest.progress.percentage - 50.0) < 1e-6
        assert quest.progress.is_complete == False


class TestInventoryManagerFixes:
    """Test inventory manager improvements"""

    def test_weight_validation(self):
        """Test carry weight validation"""
        inventory_manager = InventoryManager()
        test_inventory = []

        # This should be tested in the actual implementation
        # For now, just verify the weight calculation method exists
        total_weight = inventory_manager._calculate_total_weight(test_inventory)
        assert abs(total_weight - 0.0) < 1e-6

    def test_equipment_slot_validation(self):
        """Test equipment slot validation"""
        sword = InventoryItem(
            id=str(uuid.uuid4()),
            name="Test Sword",
            type="weapon",
            equipment_slot=EquipmentSlot.WEAPON
        )

        # The actual equipment logic would be tested here
        # For now, verify the item has the correct slot
        assert sword.equipment_slot == EquipmentSlot.WEAPON


class TestErrorHandlingFixes:
    """Test improved error handling"""

    def test_custom_exceptions_exist(self):
        """Test that custom exception types exist"""
        # Test that we can import and instantiate custom exceptions
        error = InvalidItemQuantityError("Test error")
        assert isinstance(error, Exception)
        assert str(error) == "Test error"

        error2 = InsufficientCarryCapacityError("Weight error")
        assert isinstance(error2, Exception)

    def test_validation_error_inheritance(self):
        """Test exception inheritance hierarchy"""
        error = ValidationError("Validation failed")

        # Should be a permanent error (not retryable)
        from utils.exceptions import PermanentError
        assert isinstance(error, PermanentError)


class TestStoryEntryFixes:
    """Test StoryEntry improvements"""

    def test_story_entry_has_id(self):
        """Test that StoryEntry has unique ID"""
        entry1 = StoryEntry(type=ActionType.PLAYER, text="Test action 1")
        entry2 = StoryEntry(type=ActionType.PLAYER, text="Test action 2")

        assert hasattr(entry1, 'id')
        assert hasattr(entry2, 'id')
        assert entry1.id != entry2.id
        assert len(entry1.id) > 0


class TestGameStateManagerFixes:
    """Test game state manager improvements"""

    def test_level_up_consolidation(self):
        """Test that level-up logic is consolidated"""
        # Create a character that can level up
        char = Character(name="Test", experience=150, level=1)

        # Verify the character can level up
        assert char.can_level_up() == True

        # The actual level-up logic would be in GameStateManager
        # This test verifies the helper methods work
        while char.can_level_up():
            char.level += 1
            char.max_health += 10
            char.health = char.max_health
            char.max_mana += 5
            char.mana = char.max_mana

        assert char.level == 2
        assert char.can_level_up() == False


class TestIntegrationFixes:
    """Integration tests for multiple fixes"""

    def test_complete_character_workflow(self):
        """Test complete character workflow with all fixes"""
        # Create character
        char = Character(
            name="Integration Test Hero",
            experience=0,
            level=1
        )

        # Create quest with structured progress
        objectives = ["Kill 5 goblins", "Return to village"]
        quest = Quest(
            id=str(uuid.uuid4()),
            title="Goblin Slayer",
            description="Clear out the goblin camp",
            progress=QuestProgress(
                current=0,
                total=len(objectives),
                completed_objectives=[False, False]
            ),
            objectives=objectives
        )

        # Create equipment
        sword = InventoryItem(
            id=str(uuid.uuid4()),
            name="Steel Sword",
            type="weapon",
            equipment_slot=EquipmentSlot.WEAPON,
            weight=2.5
        )

        # Verify all components work together
        assert char.can_level_up() == False  # No experience yet
        assert quest.progress.is_complete == False
        assert sword.equipment_slot == EquipmentSlot.WEAPON

        # Simulate gaining experience
        char.experience = 100
        assert char.can_level_up() == True

        # Simulate quest progress
        quest.progress.current = 1
        quest.progress.completed_objectives[0] = True
        assert abs(quest.progress.percentage - 50.0) < 1e-6

        # Complete quest
        quest.progress.current = 2
        quest.progress.completed_objectives[1] = True
        assert quest.progress.is_complete == True


if __name__ == "__main__":
    # Run basic tests
    print("Running game logic fix tests...")

    # Test character model separation
    test_char = TestCharacterModelFixes()
    test_char.test_character_vs_lore_character_separation()
    test_char.test_character_level_up_methods()
    print("✅ Character model fixes working")

    # Test equipment system
    test_equip = TestEquipmentSystemFixes()
    test_equip.test_equipment_slots_enum()
    test_equip.test_inventory_item_with_equipment_slot()
    test_equip.test_character_equipped_items_tracking()
    print("✅ Equipment system fixes working")

    # Test quest system
    test_quest = TestQuestSystemFixes()
    test_quest.test_quest_progress_structure()
    test_quest.test_quest_with_structured_progress()
    print("✅ Quest system fixes working")

    # Test error handling
    test_errors = TestErrorHandlingFixes()
    test_errors.test_custom_exceptions_exist()
    test_errors.test_validation_error_inheritance()
    print("✅ Error handling fixes working")

    # Test story entries
    test_story = TestStoryEntryFixes()
    test_story.test_story_entry_has_id()
    print("✅ Story entry fixes working")

    # Test integration
    test_integration = TestIntegrationFixes()
    test_integration.test_complete_character_workflow()
    print("✅ Integration tests passing")

    print("\n🎉 All game logic fixes are working correctly!")
